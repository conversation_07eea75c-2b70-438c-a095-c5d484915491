# Bean Software Project

## Project info

This is a React-based web application built with modern technologies for custom software development services.

## How can I edit this code?

You can edit this application using your preferred development environment.

**Local Development**

To work locally using your preferred IDE, clone this repository and follow these steps:

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

This project can be deployed to various hosting platforms such as:

- **Vercel**: Connect your GitHub repository and deploy automatically
- **Netlify**: Drag and drop your build folder or connect via Git
- **GitHub Pages**: Use GitHub Actions for automatic deployment
- **AWS S3 + CloudFront**: For scalable static hosting

To build for production:
```sh
npm run build
```

## Custom Domain Setup

Most hosting platforms allow you to connect a custom domain through their dashboard settings.
