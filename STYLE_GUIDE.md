# Bean Software - Style Guide & Design System

## Brand Colors

### Primary Palette
- **Brand Primary**: `#14353C` (HSL: 190 48% 16%)
  - Usage: Headers, navigation, icons, main text elements
  - CSS: `hsl(var(--brand-primary))`

- **Brand Accent**: `#E0B272` (HSL: 41 58% 66%)
  - Usage: Buttons, CTAs, highlights, links
  - CSS: `hsl(var(--brand-accent))`

- **Brand Accent Hover**: `#D4A55A` (HSL: 41 58% 56%)
  - Usage: Hover states for accent elements
  - CSS: `hsl(var(--brand-accent-hover))`

### Neutral Palette
- **Background**: `#F7F7F7` (HSL: 0 0% 97%)
  - Usage: Main page backgrounds
  - CSS: `hsl(var(--background))`

- **Foreground**: `#262626` (HSL: 0 0% 15%)
  - Usage: Primary text, dark charcoal
  - CSS: `hsl(var(--foreground))`

- **Muted**: `#F0F0F0` (HSL: 0 0% 94%)
  - Usage: Secondary backgrounds, cards
  - CSS: `hsl(var(--muted))`

- **Muted Foreground**: `#666666` (HSL: 0 0% 40%)
  - Usage: Secondary text, descriptions
  - CSS: `hsl(var(--muted-foreground))`

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: system-ui, sans-serif
- **CSS**: `font-family: 'Inter', system-ui, sans-serif`

### Typography Scale
```css
/* Heading Styles */
.heading-xl    { font-size: 4xl-6xl, font-weight: bold, color: primary, line-height: tight }
.heading-lg    { font-size: 3xl-4xl, font-weight: bold, color: primary, line-height: tight }
.heading-md    { font-size: 2xl-3xl, font-weight: bold, color: primary, line-height: tight }
.heading-sm    { font-size: xl-2xl, font-weight: semibold, color: primary }

/* Body Text */
body           { font-family: Inter, line-height: relaxed }
.text-lg       { font-size: 18px, used for important descriptions }
.text-base     { font-size: 16px, standard body text }
.text-sm       { font-size: 14px, used for captions and metadata }
```

### Font Weights
- **Light**: 300 (sparingly used)
- **Regular**: 400 (body text)
- **Medium**: 500 (emphasized text)
- **Semibold**: 600 (subheadings)
- **Bold**: 700 (main headings)

## Button Styles

### Primary Button (.btn-primary)
- **Background**: Brand Accent (`#E0B272`)
- **Text**: Dark text for contrast
- **Hover**: Brand Accent Hover (`#D4A55A`)
- **Usage**: Main CTAs, form submissions

### Secondary Button (.btn-secondary)
- **Background**: Brand Primary (`#14353C`)
- **Text**: White
- **Hover**: 90% opacity
- **Usage**: Secondary actions

### Outline Button (.btn-outline)
- **Border**: Brand Accent
- **Text**: Brand Accent
- **Hover**: Fill with Brand Accent
- **Usage**: Alternative actions

## Card Components

### Standard Card
```css
.card-hover {
  background: white
  border: 1px solid border-color
  border-radius: 8px
  padding: 24px
  transition: all 300ms
  hover: shadow-lg, transform: translateY(-4px)
}
```

### Feature Card
- Icon container: 64x64px, Brand Accent background
- Title: heading-sm class
- Description: muted-foreground color
- Used in: Services, features sections

## Layout & Spacing

### Container Widths
- **Max Width**: 1280px (max-w-7xl)
- **Content Width**: 896px (max-w-4xl) for text-heavy sections
- **Padding**: 16px mobile, 24px tablet, 32px desktop

### Section Spacing
- **Vertical Padding**: 64px mobile, 96px desktop (.section-padding)
- **Element Spacing**: 16px, 24px, 32px, 48px multiples
- **Grid Gaps**: 24px mobile, 32px desktop

## UI Patterns

### Navigation
- Sticky header with Brand Primary background
- Logo: Brand Accent color
- Active states: Brand Accent highlight
- Mobile: Hamburger menu with overlay

### Forms
- Input padding: 12px vertical, 16px horizontal
- Focus states: Brand Accent ring
- Required field indicators
- Validation messaging

### CTAs (Call-to-Action)
- Primary: Brand Accent buttons
- Secondary: Brand Primary buttons
- Placement: Hero sections, end of content blocks
- Text: Action-oriented ("Start Your Project", "Get Quote")

## Interactive States

### Hover Effects
- **Buttons**: Color transition (200ms)
- **Cards**: Shadow + transform (300ms)
- **Links**: Color change to Brand Accent

### Focus States
- **Keyboard Navigation**: Brand Accent outline
- **Form Fields**: Brand Accent ring shadow

## Accessibility Compliance

### Color Contrast
- Primary text on light backgrounds: 15:1 ratio
- Accent color on light backgrounds: 4.5:1 ratio
- All interactive elements meet WCAG AA standards

### Typography
- Minimum font size: 14px
- Line height: 1.6 for body text
- Adequate spacing between clickable elements (44px minimum)

## Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: > 1024px
- **Large**: > 1280px

## Design Inspiration
- **Stripe**: Clean, minimal design with excellent use of whitespace
- **Intercom**: Approachable, friendly visual style with clear hierarchy
- **Focus**: Simplicity, clarity, and user-centered design