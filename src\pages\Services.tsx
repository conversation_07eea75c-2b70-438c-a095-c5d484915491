import { Link } from 'react-router-dom';
import { Code, Smartphone, Cloud, Database, Shield, Zap, ArrowRight, CheckCircle, Users, MessageCircle, Rocket } from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: Code,
      title: "Custom Web Development",
      description: "Build powerful, scalable web applications tailored to your business needs",
      features: [
        "React, Vue.js, and Angular expertise",
        "Full-stack development with Node.js, Python, or .NET",
        "Progressive Web Apps (PWAs)",
        "E-commerce platforms and marketplaces",
        "Content Management Systems",
        "API development and integration"
      ],
      clientValue: "Increase operational efficiency by 40% with custom workflows that match exactly how your team works."
    },
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description: "Native and cross-platform mobile applications that engage your users",
      features: [
        "iOS and Android native development",
        "React Native and Flutter cross-platform",
        "Mobile-first UI/UX design",
        "App Store optimization and deployment",
        "Push notifications and analytics",
        "Offline functionality and data sync"
      ],
      clientValue: "Reach 3x more customers with mobile apps that deliver seamless experiences across all devices."
    },
    {
      icon: Cloud,
      title: "Cloud Solutions & DevOps",
      description: "Scalable cloud infrastructure and automated deployment pipelines",
      features: [
        "AWS, Azure, and Google Cloud expertise",
        "Containerization with Docker and Kubernetes",
        "CI/CD pipeline setup and optimization",
        "Infrastructure as Code (Terraform)",
        "Monitoring and alerting systems",
        "Security and compliance implementation"
      ],
      clientValue: "Reduce infrastructure costs by 30% while improving reliability and deployment speed by 10x."
    },
    {
      icon: Database,
      title: "Data & Analytics Solutions",
      description: "Transform your data into actionable insights with custom analytics platforms",
      features: [
        "Data warehouse design and implementation",
        "Real-time analytics dashboards",
        "ETL/ELT pipeline development",
        "Machine learning model integration",
        "Business intelligence reporting",
        "Data migration and cleanup"
      ],
      clientValue: "Make data-driven decisions 5x faster with real-time insights and automated reporting."
    },
    {
      icon: Shield,
      title: "Security & Compliance",
      description: "Protect your applications and data with enterprise-grade security",
      features: [
        "Security audits and penetration testing",
        "GDPR, HIPAA, and SOC 2 compliance",
        "Identity and access management",
        "Encryption and secure communications",
        "Vulnerability assessments",
        "Security training and documentation"
      ],
      clientValue: "Achieve 99.9% uptime and zero security breaches with proactive monitoring and protection."
    },
    {
      icon: Zap,
      title: "Digital Transformation",
      description: "Modernize legacy systems and optimize business processes",
      features: [
        "Legacy system migration and modernization",
        "Process automation and workflow optimization",
        "System integration and API development",
        "Digital strategy consulting",
        "Change management support",
        "Training and documentation"
      ],
      clientValue: "Accelerate business growth by 50% through streamlined processes and modern technology."
    }
  ];

  const workProcess = [
    {
      icon: MessageCircle,
      title: "Discovery & Planning",
      description: "We start by understanding your business goals, technical requirements, and success metrics. Our team conducts thorough research and creates a detailed project roadmap."
    },
    {
      icon: Code,
      title: "Development & Testing",
      description: "Using agile methodologies, we build your solution in iterative sprints. Each feature is thoroughly tested and reviewed to ensure quality and performance."
    },
    {
      icon: Rocket,
      title: "Launch & Support",
      description: "We handle deployment, monitor performance, and provide ongoing support. Your success is our success, so we're here for the long haul."
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="section-padding bg-muted">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h1 className="heading-xl mb-6">Our Services</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            From concept to deployment, we provide comprehensive software development 
            services that drive real business results. Our expert team has the skills 
            and experience to bring your vision to life.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <div key={index} className="bg-card p-8 rounded-lg border card-hover">
                <div className="w-16 h-16 bg-accent rounded-lg flex items-center justify-center mb-6">
                  <service.icon className="text-accent-foreground" size={32} />
                </div>
                
                <h3 className="heading-sm mb-4">{service.title}</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                {/* Client Value Proposition */}
                <div className="bg-accent/10 p-4 rounded-lg mb-6">
                  <p className="text-sm font-medium text-primary">
                    <strong>Client Impact:</strong> {service.clientValue}
                  </p>
                </div>
                
                {/* Features List */}
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-2">
                      <CheckCircle className="text-accent flex-shrink-0 mt-1" size={16} />
                      <span className="text-sm text-muted-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How We Work */}
      <section className="section-padding bg-muted">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="heading-lg mb-4">How We Work</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our proven three-step process ensures your project is delivered on time, 
              on budget, and exceeds your expectations
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {workProcess.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="text-accent-foreground" size={32} />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <span className="text-primary-foreground font-bold text-sm">{index + 1}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold mb-4">{step.title}</h3>
                <p className="text-muted-foreground leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="heading-lg mb-6">Why Choose Bean Software?</h2>
              
              <div className="space-y-6">
                <div className="flex space-x-4">
                  <div className="w-12 h-12 bg-accent rounded-lg flex items-center justify-center flex-shrink-0">
                    <Users className="text-accent-foreground" size={24} />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Expert Team</h3>
                    <p className="text-muted-foreground">
                      Our developers are in the top 1% of talent with 8+ years average experience
                    </p>
                  </div>
                </div>
                
                <div className="flex space-x-4">
                  <div className="w-12 h-12 bg-accent rounded-lg flex items-center justify-center flex-shrink-0">
                    <Zap className="text-accent-foreground" size={24} />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Rapid Delivery</h3>
                    <p className="text-muted-foreground">
                      Agile methodology ensures you see working software in weeks, not months
                    </p>
                  </div>
                </div>
                
                <div className="flex space-x-4">
                  <div className="w-12 h-12 bg-accent rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="text-accent-foreground" size={24} />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Enterprise Quality</h3>
                    <p className="text-muted-foreground">
                      Bank-level security and compliance standards built into every project
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-primary text-primary-foreground p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-6 text-white">Ready to Get Started?</h3>
              <p className="text-white/90 mb-6 leading-relaxed">
                Transform your business with custom software that works exactly how you need it. 
                Let's discuss your project and create a solution that drives real results.
              </p>
              
              <div className="space-y-4">
                <Link 
                  to="/contact" 
                  className="w-full bg-accent text-accent-foreground hover:bg-brand-accent-hover transition-colors duration-200 font-medium px-6 py-3 rounded-lg flex items-center justify-center"
                >
                  Start Your Project <ArrowRight className="ml-2" size={20} />
                </Link>
                <Link 
                  to="/portfolio" 
                  className="w-full bg-white/10 text-white hover:bg-white/20 transition-colors duration-200 font-medium px-6 py-3 rounded-lg flex items-center justify-center"
                >
                  View Our Work
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;