import { Link } from 'react-router-dom';
import { ExternalLink, ArrowRight, Code, Smartphone, Globe, Database } from 'lucide-react';

const Portfolio = () => {
  const projects = [
    {
      id: 1,
      title: "FinanceFlow Dashboard",
      category: "Web Application",
      summary: "Real-time financial analytics platform for investment firms with automated reporting.",
      image: "/placeholder-portfolio-1.jpg",
      icon: Database,
      technologies: ["React", "Node.js", "PostgreSQL", "AWS"],
      metrics: {
        impact: "40% faster decision making",
        users: "2,500+ active users",
        performance: "99.9% uptime"
      }
    },
    {
      id: 2,
      title: "ShopSmart E-commerce",
      category: "E-commerce Platform",
      summary: "Multi-vendor marketplace with advanced inventory management and analytics.",
      image: "/placeholder-portfolio-2.jpg",
      icon: Globe,
      technologies: ["Vue.js", "Python", "Redis", "Stripe"],
      metrics: {
        impact: "300% increase in sales",
        users: "50,000+ customers",
        performance: "2s average load time"
      }
    },
    {
      id: 3,
      title: "MedConnect Mobile",
      category: "Mobile Application",
      summary: "HIPAA-compliant telemedicine app connecting patients with healthcare providers.",
      image: "/placeholder-portfolio-3.jpg",
      icon: Smartphone,
      technologies: ["React Native", "Firebase", "WebRTC", "HIPAA"],
      metrics: {
        impact: "5x patient engagement",
        users: "10,000+ downloads",
        performance: "4.8★ app store rating"
      }
    },
    {
      id: 4,
      title: "LogiTrack Enterprise",
      category: "Enterprise Software",
      summary: "Supply chain management system with real-time tracking and predictive analytics.",
      image: "/placeholder-portfolio-4.jpg",
      icon: Code,
      technologies: ["Angular", ".NET Core", "SQL Server", "Azure"],
      metrics: {
        impact: "60% cost reduction",
        users: "1,000+ enterprise users",
        performance: "15% faster deliveries"
      }
    },
    {
      id: 5,
      title: "EduPlatform Learning",
      category: "EdTech Platform",
      summary: "Interactive learning management system with video streaming and progress tracking.",
      image: "/placeholder-portfolio-5.jpg",
      icon: Globe,
      technologies: ["Next.js", "GraphQL", "MongoDB", "Cloudflare"],
      metrics: {
        impact: "85% completion rate",
        users: "25,000+ students",
        performance: "Global CDN delivery"
      }
    },
    {
      id: 6,
      title: "WorkFlow Automation",
      category: "Business Process",
      summary: "Custom workflow automation tool reducing manual tasks by 70% for HR departments.",
      image: "/placeholder-portfolio-6.jpg",
      icon: Database,
      technologies: ["React", "Express", "MySQL", "Docker"],
      metrics: {
        impact: "70% time savings",
        users: "500+ HR professionals",
        performance: "Automated 10,000+ processes"
      }
    }
  ];

  const categories = ["All", "Web Application", "Mobile Application", "E-commerce Platform", "Enterprise Software"];
  
  return (
    <div>
      {/* Hero Section */}
      <section className="section-padding bg-muted">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h1 className="heading-xl mb-6">Our Portfolio</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            Explore our recent projects and see how we've helped businesses 
            transform their operations with custom software solutions. Each project 
            represents a unique challenge solved with innovative technology.
          </p>
        </div>
      </section>

      {/* Filter Tabs */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-6 py-2 rounded-full font-medium transition-colors duration-200 ${
                  category === "All" 
                    ? "bg-accent text-accent-foreground" 
                    : "bg-muted text-muted-foreground hover:bg-accent/20"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project) => (
              <div key={project.id} className="bg-card border rounded-lg overflow-hidden card-hover">
                {/* Project Image */}
                <div className="h-48 bg-muted flex items-center justify-center">
                  <project.icon className="text-muted-foreground" size={48} />
                </div>
                
                {/* Project Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-accent bg-accent/10 px-2 py-1 rounded">
                      {project.category}
                    </span>
                    <ExternalLink className="text-muted-foreground hover:text-primary cursor-pointer" size={16} />
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3">{project.title}</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {project.summary}
                  </p>
                  
                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech, index) => (
                      <span 
                        key={index}
                        className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  
                  {/* Metrics */}
                  <div className="space-y-2 mb-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Impact:</span>
                      <span className="font-medium text-accent">{project.metrics.impact}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Users:</span>
                      <span className="font-medium">{project.metrics.users}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Performance:</span>
                      <span className="font-medium">{project.metrics.performance}</span>
                    </div>
                  </div>
                  
                  <button className="w-full bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 font-medium px-4 py-2 rounded-lg flex items-center justify-center">
                    View Details <ArrowRight className="ml-2" size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Study Highlight */}
      <section className="section-padding bg-muted">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="bg-white p-8 md:p-12 rounded-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <span className="text-accent font-medium">Featured Case Study</span>
                <h2 className="heading-lg mt-2 mb-6">FinanceFlow Dashboard Success Story</h2>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  See how we helped a leading investment firm transform their data analysis 
                  process with a custom dashboard that processes over 1 million transactions 
                  daily and provides real-time insights to portfolio managers.
                </p>
                
                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div>
                    <div className="text-3xl font-bold text-accent mb-1">40%</div>
                    <div className="text-sm text-muted-foreground">Faster Decisions</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-1">$2M</div>
                    <div className="text-sm text-muted-foreground">Cost Savings</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-1">99.9%</div>
                    <div className="text-sm text-muted-foreground">Uptime</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-1">6mo</div>
                    <div className="text-sm text-muted-foreground">ROI Timeline</div>
                  </div>
                </div>
                
                <button className="btn-primary">
                  Read Full Case Study
                </button>
              </div>
              
              <div className="h-80 bg-muted rounded-lg flex items-center justify-center">
                <Database className="text-muted-foreground" size={64} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Build Something Amazing?
          </h2>
          <p className="text-xl mb-8 text-white/90">
            Let's discuss your project and explore how we can help you achieve similar results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact" className="btn-primary">
              Start Your Project
            </Link>
            <Link to="/services" className="bg-white/10 text-white hover:bg-white/20 transition-colors duration-200 font-medium px-6 py-3 rounded-lg">
              Explore Services
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portfolio;