@tailwind base;
@tailwind components;
@tailwind utilities;

/* Bean Software Design System
Primary: #14353C - Dark teal for headers, navigation, icons
Accent: #E0B272 - Gold for buttons, CTAs, highlights  
Neutrals: Light grays and dark charcoal
*/

@layer base {
  :root {
    /* Brand Colors */
    --brand-primary: 190 48% 16%; /* #14353C */
    --brand-accent: 41 58% 66%; /* #E0B272 */
    --brand-accent-hover: 41 58% 56%; /* Darker gold for hover */
    
    /* Semantic Colors */
    --background: 0 0% 97%; /* #F7F7F7 light gray */
    --foreground: 0 0% 15%; /* Dark charcoal */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 15%;

    --primary: 190 48% 16%; /* Brand primary */
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 94%;
    --secondary-foreground: 0 0% 15%;

    --muted: 0 0% 94%;
    --muted-foreground: 0 0% 40%;

    --accent: 41 58% 66%; /* Brand accent */
    --accent-foreground: 0 0% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 100%;
    --ring: 190 48% 16%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter leading-relaxed;
  }
}

@layer components {
  /* Button Variants */
  .btn-primary {
    @apply bg-accent text-accent-foreground hover:bg-[hsl(var(--brand-accent-hover))] transition-colors duration-200 font-medium px-6 py-3 rounded-lg;
  }
  
  .btn-secondary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 font-medium px-6 py-3 rounded-lg;
  }
  
  .btn-outline {
    @apply border-2 border-accent text-accent hover:bg-accent hover:text-accent-foreground transition-colors duration-200 font-medium px-6 py-3 rounded-lg;
  }

  /* Typography */
  .heading-xl {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-primary leading-tight;
  }
  
  .heading-lg {
    @apply text-3xl md:text-4xl font-bold text-primary leading-tight;
  }
  
  .heading-md {
    @apply text-2xl md:text-3xl font-bold text-primary leading-tight;
  }
  
  .heading-sm {
    @apply text-xl md:text-2xl font-semibold text-primary;
  }

  /* Cards */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Sections */
  .section-padding {
    @apply py-16 md:py-24;
  }
  
  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }
}