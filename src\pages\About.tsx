import { Award, Heart, Target, Users, CheckCircle } from 'lucide-react';

const About = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "Full-stack developer with 12+ years building scalable web applications. Former tech lead at three successful startups.",
      image: "/placeholder-team-1.jpg"
    },
    {
      name: "<PERSON>",
      role: "CTO & Lead Developer",
      bio: "Mobile and cloud architecture specialist. Expert in React, Node.js, and AWS with a passion for clean, maintainable code.",
      image: "/placeholder-team-2.jpg"
    },
    {
      name: "<PERSON>",
      role: "Senior UX Developer",
      bio: "Frontend specialist focused on creating intuitive user experiences. 8+ years designing and building user-centric applications.",
      image: "/placeholder-team-3.jpg"
    },
    {
      name: "<PERSON>",
      role: "DevOps Engineer",
      bio: "Infrastructure and deployment expert ensuring our applications are secure, scalable, and performant in production environments.",
      image: "/placeholder-team-4.jpg"
    }
  ];

  const values = [
    {
      icon: CheckCircle,
      title: "Quality First",
      description: "We write clean, maintainable code and thoroughly test every feature to ensure reliability."
    },
    {
      icon: Users,
      title: "Client Partnership",
      description: "Your success is our success. We work as an extension of your team, not just a vendor."
    },
    {
      icon: Target,
      title: "Results Driven",
      description: "Every line of code serves a business purpose. We focus on delivering measurable value."
    },
    {
      icon: Heart,
      title: "Passionate Craftsmanship",
      description: "We love what we do and it shows in the elegant solutions we create for complex problems."
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="section-padding bg-muted">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h1 className="heading-xl mb-6">About Bean Software</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            We're a team of passionate developers who believe that great software 
            can transform businesses and improve lives. Founded in 2019, we've helped 
            over 200 companies bring their digital visions to reality.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="heading-lg mb-6">Our Mission</h2>
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                To democratize access to world-class software development by making 
                custom solutions accessible to businesses of all sizes. We believe 
                every company deserves software that fits their unique needs perfectly.
              </p>
              
              <h3 className="heading-sm mb-4">Our Vision</h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                A world where businesses can focus on what they do best while 
                technology seamlessly supports their growth and innovation. We're 
                building that future, one application at a time.
              </p>
            </div>
            
            <div className="bg-accent/10 p-8 rounded-lg">
              <div className="text-center mb-6">
                <Award className="text-accent mx-auto mb-4" size={48} />
                <h3 className="heading-sm">Award-Winning Team</h3>
              </div>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <CheckCircle className="text-accent mr-3 flex-shrink-0" size={20} />
                  200+ successful projects delivered
                </li>
                <li className="flex items-center">
                  <CheckCircle className="text-accent mr-3 flex-shrink-0" size={20} />
                  98% client satisfaction rate
                </li>
                <li className="flex items-center">
                  <CheckCircle className="text-accent mr-3 flex-shrink-0" size={20} />
                  50+ ongoing support relationships
                </li>
                <li className="flex items-center">
                  <CheckCircle className="text-accent mr-3 flex-shrink-0" size={20} />
                  Top 1% developer talent
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="section-padding bg-muted">
        <div className="max-w-4xl mx-auto container-padding">
          <h2 className="heading-lg text-center mb-12">Our Story</h2>
          
          <div className="space-y-8">
            <div className="bg-white p-8 rounded-lg">
              <h3 className="text-2xl font-semibold mb-4 text-primary">The Beginning (2019)</h3>
              <p className="text-muted-foreground leading-relaxed">
                Bean Software started when our founder, Sarah Chen, left her corporate job after 
                watching too many businesses struggle with expensive, inflexible software solutions. 
                She envisioned a development company that would prioritize client success over 
                project scope, quality over speed, and long-term partnerships over one-time deals.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg">
              <h3 className="text-2xl font-semibold mb-4 text-primary">Growing the Team (2020-2022)</h3>
              <p className="text-muted-foreground leading-relaxed">
                As word spread about our unique approach, we carefully selected team members who 
                shared our values. Each developer we hired brought not just technical expertise, 
                but a genuine passion for solving client problems and delivering exceptional results. 
                By 2022, we had grown to a team of 15 specialists.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg">
              <h3 className="text-2xl font-semibold mb-4 text-primary">Today & Tomorrow (2023+)</h3>
              <p className="text-muted-foreground leading-relaxed">
                Today, Bean Software is recognized as a leader in custom software development. 
                We've expanded our services to include mobile development, cloud solutions, and 
                DevOps consulting while maintaining our commitment to personal service and 
                technical excellence. Looking ahead, we're excited to help even more businesses 
                achieve their digital transformation goals.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="heading-lg mb-4">What Sets Us Apart</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our core values guide every decision we make and every line of code we write
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="flex space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-accent rounded-lg flex items-center justify-center">
                    <value.icon className="text-accent-foreground" size={24} />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="section-padding bg-muted">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="heading-lg mb-4">Meet Our Team</h2>
            <p className="text-lg text-muted-foreground">
              The talented individuals behind Bean Software's success
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white p-6 rounded-lg text-center card-hover">
                <div className="w-24 h-24 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Users className="text-muted-foreground" size={32} />
                </div>
                <h3 className="text-xl font-semibold mb-2">{member.name}</h3>
                <p className="text-accent font-medium mb-3">{member.role}</p>
                <p className="text-sm text-muted-foreground leading-relaxed">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;