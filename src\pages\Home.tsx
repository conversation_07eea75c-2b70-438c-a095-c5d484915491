import { Link } from 'react-router-dom';
import { ArrowRight, Code, Smartphone, Cloud, Zap, Shield, Users } from 'lucide-react';
import heroImage from '../assets/hero-image.jpg';

const Home = () => {
  return (
    <div>
      {/* Hero Section */}
      <section 
        className="relative min-h-screen flex items-center justify-center text-center"
        style={{
          backgroundImage: `linear-gradient(rgba(20, 53, 60, 0.7), rgba(20, 53, 60, 0.7)), url(${heroImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div className="max-w-4xl mx-auto container-padding text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white">
            We Make Ideas Happen
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white/90 leading-relaxed">
            Transform your vision into powerful software solutions. We build custom web applications, 
            mobile apps, and digital platforms that drive real business results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/services" className="btn-primary inline-flex items-center">
              Explore Services <ArrowRight className="ml-2" size={20} />
            </Link>
            <Link to="/contact" className="btn-outline inline-flex items-center">
              Request a Quote <ArrowRight className="ml-2" size={20} />
            </Link>
          </div>
        </div>
      </section>

      {/* Company Elevator Pitch */}
      <section className="section-padding bg-white">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h2 className="heading-lg mb-8">
            Building Software That Powers Success
          </h2>
          <p className="text-lg text-muted-foreground leading-relaxed">
            At Bean Software, we're not just developers – we're your technology partners. 
            We combine technical expertise with business insight to create software that doesn't 
            just work, but works for you. From startups to enterprises, we've helped hundreds 
            of companies turn their ideas into market-leading digital products.
          </p>
        </div>
      </section>

      {/* Core Services */}
      <section className="section-padding bg-muted">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="heading-lg mb-4">How We Drive Your Success</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our expert team delivers solutions that scale with your business
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Custom Web Development */}
            <div className="bg-white p-8 rounded-lg card-hover">
              <div className="w-16 h-16 bg-accent rounded-lg flex items-center justify-center mb-6">
                <Code className="text-accent-foreground" size={32} />
              </div>
              <h3 className="heading-sm mb-4">Custom Web Development</h3>
              <p className="text-muted-foreground leading-relaxed">
                Build powerful, scalable web applications that give you a competitive edge. 
                Our full-stack expertise ensures your platform can handle growth while 
                delivering exceptional user experiences.
              </p>
            </div>

            {/* Mobile App Development */}
            <div className="bg-white p-8 rounded-lg card-hover">
              <div className="w-16 h-16 bg-accent rounded-lg flex items-center justify-center mb-6">
                <Smartphone className="text-accent-foreground" size={32} />
              </div>
              <h3 className="heading-sm mb-4">Mobile App Development</h3>
              <p className="text-muted-foreground leading-relaxed">
                Reach your customers wherever they are with native and cross-platform mobile apps. 
                We create intuitive, high-performance applications that drive engagement and 
                boost your bottom line.
              </p>
            </div>

            {/* Cloud & DevOps */}
            <div className="bg-white p-8 rounded-lg card-hover">
              <div className="w-16 h-16 bg-accent rounded-lg flex items-center justify-center mb-6">
                <Cloud className="text-accent-foreground" size={32} />
              </div>
              <h3 className="heading-sm mb-4">Cloud & DevOps Solutions</h3>
              <p className="text-muted-foreground leading-relaxed">
                Scale efficiently and reduce costs with modern cloud architecture. 
                Our DevOps practices ensure your applications are reliable, secure, 
                and ready to handle whatever comes next.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="heading-lg mb-4">Why Leading Companies Choose Bean Software</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="text-primary-foreground" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Lightning Fast Delivery</h3>
              <p className="text-muted-foreground">
                Agile development process that delivers working software in weeks, not months
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="text-primary-foreground" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Enterprise Security</h3>
              <p className="text-muted-foreground">
                Bank-level security practices and compliance standards built into every project
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="text-primary-foreground" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Dedicated Support</h3>
              <p className="text-muted-foreground">
                Ongoing maintenance and support to keep your software running smoothly
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Transform Your Ideas?
          </h2>
          <p className="text-xl mb-8 text-white/90">
            Let's discuss your project and explore how we can help you achieve your goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact" className="btn-primary">
              Start Your Project
            </Link>
            <Link to="/portfolio" className="bg-white/10 text-white hover:bg-white/20 transition-colors duration-200 font-medium px-6 py-3 rounded-lg">
              View Our Work
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;